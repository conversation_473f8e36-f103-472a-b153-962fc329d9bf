"""
Individual data detail views for admin.
"""
import flet as ft
import sqlite3
from gui.components.admin_layout import create_admin_page_layout
from facial_recognition_system.config import Config
from gui.config.constants import ROUTE_LOGIN

def create_data_table(title: str, data: list, columns: list, icon: str = ft.Icons.TABLE_CHART, page=None):
    """Create a modern, centered data table for a specific data type."""
    is_mobile = getattr(page, 'is_mobile', False) if page else False

    if not data:
        return ft.Container(
            content=ft.Column([
                ft.Container(
                    content=ft.Icon(
                        ft.Icons.INBOX,
                        size=48,
                        color=ft.Colors.BLUE_300
                    ),
                    width=96,
                    height=96,
                    bgcolor=ft.Colors.BLUE_50,
                    border_radius=48,
                    alignment=ft.alignment.center
                ),
                ft.Text(
                    f"Aucune donnée {title.lower()}",
                    size=18,
                    color=ft.Colors.BLUE_900,
                    text_align=ft.TextAlign.CENTER,
                    weight=ft.FontWeight.BOLD
                ),
                ft.Text(
                    f"Les données {title.lower()} apparaîtront ici",
                    size=14,
                    color=ft.Colors.GREY_600,
                    text_align=ft.TextAlign.CENTER
                )
            ], spacing=16, horizontal_alignment=ft.CrossAxisAlignment.CENTER),
            alignment=ft.alignment.center,
            padding=ft.padding.all(48),
            bgcolor=ft.Colors.WHITE,
            border_radius=ft.border_radius.all(16),
            margin=ft.margin.only(bottom=24),
            shadow=ft.BoxShadow(
                spread_radius=0,
                blur_radius=12,
                color=ft.Colors.with_opacity(0.08, ft.Colors.BLUE_600),
                offset=ft.Offset(0, 4)
            ),
            border=ft.border.all(1, ft.Colors.BLUE_100),
            # expand=True,
            width=1200,  # Fixed narrower width
        )

    # Create table rows with modern styling
    rows = []
    for i, item in enumerate(data):
        cells = []
        for col in columns:
            value = str(item.get(col, ''))
            if len(value) > 30:
                value = value[:27] + "..."
            cells.append(ft.DataCell(
                ft.Text(
                    value,
                    size=13,
                    color=ft.Colors.GREY_800,
                    weight=ft.FontWeight.W_400
                )
            ))

        # Cleaner alternate row colors
        row_color = ft.Colors.GREY_50 if i % 2 == 0 else ft.Colors.WHITE
        rows.append(ft.DataRow(cells=cells, color=row_color))

    # Create table columns with modern styling
    table_columns = [
        ft.DataColumn(
            ft.Text(
                col.replace('_', ' ').title(),
                weight=ft.FontWeight.BOLD,
                size=14,
                color=ft.Colors.BLUE_900
            )
        ) for col in columns
    ]

    return ft.Container(
        content=ft.Column([
            # Header with icon and title
            ft.Row([
                ft.Container(
                    content=ft.Icon(
                        icon,
                        size=24,
                        color=ft.Colors.BLUE_600
                    ),
                    width=40,
                    height=40,
                    bgcolor=ft.Colors.BLUE_50,
                    border_radius=20,
                    alignment=ft.alignment.center
                ),
                ft.Column([
                    ft.Text(
                        title,
                        size=20,
                        weight=ft.FontWeight.BOLD,
                        color=ft.Colors.BLUE_900
                    ),

                ], spacing=2, expand=True)
            ], spacing=12, alignment=ft.MainAxisAlignment.START),

            ft.Container(height=16),

            # Modern data table with improved design
            ft.Container(
                content=ft.DataTable(
                    columns=table_columns,
                    rows=rows,
                    border=ft.border.all(1, ft.Colors.GREY_300),
                    border_radius=12,
                    vertical_lines=ft.BorderSide(1, ft.Colors.GREY_200),
                    horizontal_lines=ft.BorderSide(1, ft.Colors.GREY_200),
                    heading_row_color=ft.Colors.BLUE_50,
                    heading_row_height=48,
                    data_row_min_height=42,
                    data_row_max_height=42,
                    column_spacing=12,
                    horizontal_margin=8,
                    show_checkbox_column=False,
                ),
                bgcolor=ft.Colors.WHITE,
                border_radius=12,
                padding=ft.padding.all(12),
                shadow=ft.BoxShadow(
                    spread_radius=0,
                    blur_radius=8,
                    color=ft.Colors.with_opacity(0.08, ft.Colors.GREY_600),
                    offset=ft.Offset(0, 2)
                ),
                alignment=ft.alignment.center,
            )
        ], spacing=0),
        width=800,  # Fixed narrower width for better design
        padding=ft.padding.all(20),
        margin=ft.margin.only(bottom=24),
        bgcolor=ft.Colors.WHITE,
        border_radius=ft.border_radius.all(16),
        shadow=ft.BoxShadow(
            spread_radius=0,
            blur_radius=12,
            color=ft.Colors.with_opacity(0.08, ft.Colors.GREY_600),
            offset=ft.Offset(0, 4)
        ),
        border=ft.border.all(1, ft.Colors.GREY_200),
        alignment=ft.alignment.center,
    )

def get_data_by_type(data_type: str):
    """Get specific data from the database."""
    try:
        with sqlite3.connect(Config.get_db_path()) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()

            if data_type == 'classes':
                cursor.execute("SELECT * FROM classes ORDER BY name")
                return [dict(row) for row in cursor.fetchall()]

            elif data_type == 'students':
                cursor.execute("""
                    SELECT e.*, c.name as class_name
                    FROM etudiants e
                    LEFT JOIN classes c ON e.class_id = c.id
                    ORDER BY e.name
                """)
                return [dict(row) for row in cursor.fetchall()]

            elif data_type == 'subjects':
                cursor.execute("""
                    SELECT m.*, c.name as class_name
                    FROM matieres m
                    LEFT JOIN classes c ON m.class_id = c.id
                    ORDER BY m.name
                """)
                return [dict(row) for row in cursor.fetchall()]

            elif data_type == 'quizzes':
                cursor.execute("""
                    SELECT q.*, c.name as class_name, m.name as subject_name
                    FROM quiz q
                    LEFT JOIN classes c ON q.class_id = c.id
                    LEFT JOIN matieres m ON q.subject_id = m.id
                    ORDER BY q.created_at DESC
                """)
                return [dict(row) for row in cursor.fetchall()]

            elif data_type == 'attendance':
                cursor.execute("""
                    SELECT p.*, e.name as student_name, c.name as class_name, m.name as subject_name
                    FROM presences p
                    LEFT JOIN etudiants e ON p.student_id = e.id
                    LEFT JOIN classes c ON p.class_id = c.id
                    LEFT JOIN matieres m ON p.subject_id = m.id
                    ORDER BY p.date DESC, p.time DESC
                    LIMIT 200
                """)
                return [dict(row) for row in cursor.fetchall()]

            return []

    except Exception as e:
        print(f"❌ Failed to get {data_type} data: {e}")
        return []

def create_classes_with_students_table(classes_data: list, page: ft.Page):
    """Create a modern classes table with integrated student counts and clickable rows."""
    if not classes_data:
        return ft.Container(
            content=ft.Text(
                "Aucune classe trouvée",
                size=16,
                color=ft.Colors.GREY_600,
                text_align=ft.TextAlign.CENTER
            ),
            alignment=ft.alignment.center,
            padding=ft.padding.all(40)
        )

    # Get student counts for each class
    try:
        with sqlite3.connect(Config.get_db_path()) as conn:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT class_id, COUNT(*) as student_count
                FROM etudiants
                WHERE class_id IS NOT NULL
                GROUP BY class_id
            """)
            student_counts = {row[0]: row[1] for row in cursor.fetchall()}
    except Exception as e:
        print(f"❌ Failed to get student counts: {e}")
        student_counts = {}

    # Create table rows with modern styling and click handlers
    rows = []
    for i, class_item in enumerate(classes_data):
        class_id = class_item.get('id')
        class_name = class_item.get('name', '')
        description = class_item.get('description', '')
        created_at = class_item.get('created_at', '')
        student_count = student_counts.get(class_id, 0)

        # Truncate long text
        if len(class_name) > 25:
            class_name = class_name[:22] + "..."
        if len(description) > 30:
            description = description[:27] + "..."
        if len(created_at) > 19:
            created_at = created_at[:19]

        def create_click_handler(class_id, _class_name):
            def handle_click(_):
                page.go(f"/admin/classes/{class_id}/students")
            return handle_click

        cells = [
            ft.DataCell(
                ft.Text(
                    class_name,
                    size=13,
                    color=ft.Colors.BLUE_700,
                    weight=ft.FontWeight.W_500
                )
            ),
            ft.DataCell(
                ft.Text(
                    description,
                    size=13,
                    color=ft.Colors.GREY_800,
                    weight=ft.FontWeight.W_400
                )
            ),
            ft.DataCell(
                ft.Container(
                    content=ft.Row([
                        ft.Icon(ft.Icons.PEOPLE, size=16, color=ft.Colors.GREEN_600),
                        ft.Text(
                            str(student_count),
                            size=13,
                            color=ft.Colors.GREEN_700,
                            weight=ft.FontWeight.W_600
                        )
                    ], spacing=4),
                    padding=ft.padding.symmetric(horizontal=8, vertical=4),
                    bgcolor=ft.Colors.GREEN_50,
                    border_radius=8
                )
            ),
            ft.DataCell(
                ft.Text(
                    created_at,
                    size=13,
                    color=ft.Colors.GREY_600,
                    weight=ft.FontWeight.W_400
                )
            )
        ]

        # Cleaner alternate row colors with hover effect
        row_color = ft.Colors.GREY_50 if i % 2 == 0 else ft.Colors.WHITE
        rows.append(ft.DataRow(
            cells=cells,
            color=row_color,
            on_select_changed=create_click_handler(class_id, class_item.get('name', ''))
        ))

    # Create table columns with modern styling
    table_columns = [
        ft.DataColumn(
            ft.Text(
                "Nom de la Classe",
                weight=ft.FontWeight.BOLD,
                size=14,
                color=ft.Colors.BLUE_900
            )
        ),
        ft.DataColumn(
            ft.Text(
                "Description",
                weight=ft.FontWeight.BOLD,
                size=14,
                color=ft.Colors.BLUE_900
            )
        ),
        ft.DataColumn(
            ft.Text(
                "Étudiants",
                weight=ft.FontWeight.BOLD,
                size=14,
                color=ft.Colors.BLUE_900
            )
        ),
        ft.DataColumn(
            ft.Text(
                "Créé le",
                weight=ft.FontWeight.BOLD,
                size=14,
                color=ft.Colors.BLUE_900
            )
        )
    ]

    # Create the main container with title and table
    return ft.Column([
        # Title section with icon
        ft.Container(
            content=ft.Row([
                ft.Icon(ft.Icons.SCHOOL, size=28, color=ft.Colors.BLUE_600),
                ft.Text(
                    "Classes",
                    size=24,
                    weight=ft.FontWeight.BOLD,
                    color=ft.Colors.BLUE_900
                ),
                ft.Container(
                    content=ft.Text(
                        f"{len(classes_data)} classe{'s' if len(classes_data) != 1 else ''}",
                        size=14,
                        color=ft.Colors.GREY_600,
                        weight=ft.FontWeight.W_500
                    ),
                    padding=ft.padding.symmetric(horizontal=12, vertical=4),
                    bgcolor=ft.Colors.BLUE_50,
                    border_radius=12
                )
            ], spacing=12),
            margin=ft.margin.only(bottom=16)
        ),

        # Info text
        ft.Container(
            content=ft.Text(
                "Cliquez sur une classe pour voir ses étudiants",
                size=14,
                color=ft.Colors.GREY_600,
                italic=True
            ),
            margin=ft.margin.only(bottom=16)
        ),

        # Modern data table with improved design
        ft.Container(
            content=ft.DataTable(
                columns=table_columns,
                rows=rows,
                border=ft.border.all(1, ft.Colors.GREY_300),
                border_radius=12,
                vertical_lines=ft.BorderSide(1, ft.Colors.GREY_200),
                horizontal_lines=ft.BorderSide(1, ft.Colors.GREY_200),
                heading_row_color=ft.Colors.BLUE_50,
                heading_row_height=48,
                data_row_min_height=52,
                data_row_max_height=52,
                column_spacing=16,
                horizontal_margin=12,
                show_checkbox_column=False,
            ),
            bgcolor=ft.Colors.WHITE,
            border_radius=12,
            padding=ft.padding.all(12),
            shadow=ft.BoxShadow(
                spread_radius=0,
                blur_radius=8,
                color=ft.Colors.with_opacity(0.08, ft.Colors.GREY_600),
                offset=ft.Offset(0, 2)
            ),
            alignment=ft.alignment.center,
        )
    ], spacing=0)

def create_admin_classes_view(page: ft.Page):
    """Create the admin classes detail view with integrated students."""
    current_user = getattr(page.app_state, 'current_user', None)
    if not current_user or current_user.get('role') != 'admin':
        page.go(ROUTE_LOGIN)
        return ft.View(route="/admin/classes", controls=[])

    # Get classes data
    classes_data = get_data_by_type('classes')

    # Create classes table with integrated students
    content = [
        create_classes_with_students_table(classes_data, page)
    ]

    return create_admin_page_layout(
        page,
        "Classes",
        content
    )

def create_class_students_table(class_info: dict, students_data: list):
    """Create a modern table displaying students in a specific class."""

    # Class info header
    class_header = ft.Container(
        content=ft.Column([
            ft.Row([
                ft.Icon(ft.Icons.SCHOOL, size=32, color=ft.Colors.BLUE_600),
                ft.Column([
                    ft.Text(
                        class_info['name'],
                        size=28,
                        weight=ft.FontWeight.BOLD,
                        color=ft.Colors.BLUE_900
                    ),
                    ft.Text(
                        class_info.get('description', 'Aucune description'),
                        size=16,
                        color=ft.Colors.GREY_600
                    ) if class_info.get('description') else ft.Container()
                ], spacing=4, expand=True)
            ], spacing=16),

            ft.Container(
                content=ft.Row([
                    ft.Icon(ft.Icons.PEOPLE, size=20, color=ft.Colors.GREEN_600),
                    ft.Text(
                        f"{len(students_data)} étudiant{'s' if len(students_data) != 1 else ''}",
                        size=16,
                        color=ft.Colors.GREEN_700,
                        weight=ft.FontWeight.W_600
                    )
                ], spacing=8),
                padding=ft.padding.symmetric(horizontal=16, vertical=8),
                bgcolor=ft.Colors.GREEN_50,
                border_radius=12,
                margin=ft.margin.only(top=16)
            )
        ], spacing=0),
        padding=ft.padding.all(20),
        bgcolor=ft.Colors.BLUE_50,
        border_radius=12,
        margin=ft.margin.only(bottom=24)
    )

    if not students_data:
        empty_state = ft.Container(
            content=ft.Column([
                ft.Icon(ft.Icons.PEOPLE_OUTLINE, size=64, color=ft.Colors.GREY_400),
                ft.Text(
                    "Aucun étudiant dans cette classe",
                    size=18,
                    color=ft.Colors.GREY_600,
                    weight=ft.FontWeight.W_500,
                    text_align=ft.TextAlign.CENTER
                ),
                ft.Text(
                    "Les étudiants apparaîtront ici une fois ajoutés à cette classe",
                    size=14,
                    color=ft.Colors.GREY_500,
                    text_align=ft.TextAlign.CENTER
                )
            ], spacing=12, horizontal_alignment=ft.CrossAxisAlignment.CENTER),
            alignment=ft.alignment.center,
            padding=ft.padding.all(60),
            bgcolor=ft.Colors.WHITE,
            border_radius=12,
            border=ft.border.all(1, ft.Colors.GREY_200)
        )

        return ft.Column([class_header, empty_state], spacing=0)

    # Create table rows
    rows = []
    for i, student in enumerate(students_data):
        student_name = student.get('name', '')
        created_at = student.get('created_at', '')
        updated_at = student.get('updated_at', '')

        # Truncate long names
        if len(student_name) > 30:
            student_name = student_name[:27] + "..."
        if len(created_at) > 19:
            created_at = created_at[:19]
        if len(updated_at) > 19:
            updated_at = updated_at[:19]

        cells = [
            ft.DataCell(
                ft.Row([
                    ft.Icon(ft.Icons.PERSON, size=18, color=ft.Colors.BLUE_600),
                    ft.Text(
                        student_name,
                        size=14,
                        color=ft.Colors.BLUE_700,
                        weight=ft.FontWeight.W_500
                    )
                ], spacing=8)
            ),
            ft.DataCell(
                ft.Text(
                    created_at,
                    size=13,
                    color=ft.Colors.GREY_700,
                    weight=ft.FontWeight.W_400
                )
            ),
            ft.DataCell(
                ft.Text(
                    updated_at if updated_at else "Jamais",
                    size=13,
                    color=ft.Colors.GREY_600,
                    weight=ft.FontWeight.W_400
                )
            )
        ]

        # Cleaner alternate row colors
        row_color = ft.Colors.GREY_50 if i % 2 == 0 else ft.Colors.WHITE
        rows.append(ft.DataRow(cells=cells, color=row_color))

    # Create table columns
    table_columns = [
        ft.DataColumn(
            ft.Text(
                "Nom de l'Étudiant",
                weight=ft.FontWeight.BOLD,
                size=14,
                color=ft.Colors.BLUE_900
            )
        ),
        ft.DataColumn(
            ft.Text(
                "Ajouté le",
                weight=ft.FontWeight.BOLD,
                size=14,
                color=ft.Colors.BLUE_900
            )
        ),
        ft.DataColumn(
            ft.Text(
                "Modifié le",
                weight=ft.FontWeight.BOLD,
                size=14,
                color=ft.Colors.BLUE_900
            )
        )
    ]

    # Create the students table
    students_table = ft.Container(
        content=ft.DataTable(
            columns=table_columns,
            rows=rows,
            border=ft.border.all(1, ft.Colors.GREY_300),
            border_radius=12,
            vertical_lines=ft.BorderSide(1, ft.Colors.GREY_200),
            horizontal_lines=ft.BorderSide(1, ft.Colors.GREY_200),
            heading_row_color=ft.Colors.BLUE_50,
            heading_row_height=48,
            data_row_min_height=48,
            data_row_max_height=48,
            column_spacing=20,
            horizontal_margin=16,
            show_checkbox_column=False,
        ),
        bgcolor=ft.Colors.WHITE,
        border_radius=12,
        padding=ft.padding.all(12),
        shadow=ft.BoxShadow(
            spread_radius=0,
            blur_radius=8,
            color=ft.Colors.with_opacity(0.08, ft.Colors.GREY_600),
            offset=ft.Offset(0, 2)
        ),
        alignment=ft.alignment.center,
    )

    return ft.Column([class_header, students_table], spacing=0)

def create_admin_class_students_view(page: ft.Page, class_id: int):
    """Create the admin view for students in a specific class."""
    current_user = getattr(page.app_state, 'current_user', None)
    if not current_user or current_user.get('role') != 'admin':
        page.go(ROUTE_LOGIN)
        return ft.View(route=f"/admin/classes/{class_id}/students", controls=[])

    # Get class information
    try:
        with sqlite3.connect(Config.get_db_path()) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()

            # Get class details
            cursor.execute("SELECT * FROM classes WHERE id = ?", (class_id,))
            class_row = cursor.fetchone()

            if not class_row:
                # Class not found, redirect back to classes
                page.go("/admin/classes")
                return ft.View(route=f"/admin/classes/{class_id}/students", controls=[])

            class_info = dict(class_row)

            # Get students in this class
            cursor.execute("""
                SELECT id, name, created_at, updated_at
                FROM etudiants
                WHERE class_id = ?
                ORDER BY name
            """, (class_id,))
            students_data = [dict(row) for row in cursor.fetchall()]

    except Exception as e:
        print(f"❌ Failed to get class data: {e}")
        page.go("/admin/classes")
        return ft.View(route=f"/admin/classes/{class_id}/students", controls=[])

    # Create back button
    def go_back(_):
        page.go("/admin/classes")

    back_button = ft.Container(
        content=ft.ElevatedButton(
            text="← Retour aux Classes",
            icon=ft.Icons.ARROW_BACK,
            style=ft.ButtonStyle(
                color=ft.Colors.BLUE_600,
                bgcolor=ft.Colors.BLUE_50,
                text_style=ft.TextStyle(
                    weight=ft.FontWeight.W_600,
                    size=14
                ),
                shape=ft.RoundedRectangleBorder(radius=8),
                padding=ft.padding.symmetric(horizontal=16, vertical=12)
            ),
            on_click=go_back
        ),
        margin=ft.margin.only(bottom=20)
    )

    # Create students table
    students_table = create_class_students_table(class_info, students_data)

    content = [
        back_button,
        students_table
    ]

    return create_admin_page_layout(
        page,
        f"Étudiants - {class_info['name']}",
        content
    )

def create_admin_subjects_view(page: ft.Page):
    """Create the admin subjects detail view with quizzes and attendance."""
    current_user = getattr(page.app_state, 'current_user', None)
    if not current_user or current_user.get('role') != 'admin':
        page.go(ROUTE_LOGIN)
        return ft.View(route="/admin/subjects", controls=[])

    # Get subjects, quizzes, and attendance data
    subjects_data = get_data_by_type('subjects')
    quizzes_data = get_data_by_type('quizzes')
    attendance_data = get_data_by_type('attendance')

    content = [
        create_data_table("Matières", subjects_data, ['name', 'class_name', 'description', 'created_at'], ft.Icons.BOOK, page),
        create_data_table("Quiz", quizzes_data, ['title', 'class_name', 'subject_name', 'created_at'], ft.Icons.QUIZ, page),
        create_data_table("Présences", attendance_data, ['student_name', 'class_name', 'subject_name', 'status', 'date', 'time'], ft.Icons.CHECK_CIRCLE, page)
    ]

    return create_admin_page_layout(
        page,
        "Matières, Quiz et Présences",
        content
    )
