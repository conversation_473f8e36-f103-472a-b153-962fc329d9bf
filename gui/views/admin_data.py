"""
Admin data overview view showing all system data.
"""
import flet as ft
import sqlite3
from gui.components.admin_layout import create_admin_page_layout
from facial_recognition_system.config import Config
from gui.config.constants import ROUTE_LOGIN, ROUTE_ADMIN_DASHBOARD
from gui.config.language import get_text

def create_admin_data_view(page: ft.Page):
    """Create the admin data overview view."""
    current_language = getattr(page, 'language', 'en')
    is_mobile = getattr(page, 'is_mobile', False)

    # Check if user is admin
    current_user = getattr(page.app_state, 'current_user', None)
    if not current_user or current_user.get('role') != 'admin':
        page.go(ROUTE_LOGIN)
        return ft.View(route="/admin/data", controls=[])

    def get_all_data():
        """Get all data from the database."""
        try:
            with sqlite3.connect(Config.get_db_path()) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()

                data = {}

                # Get classes
                cursor.execute("SELECT * FROM classes ORDER BY name")
                data['classes'] = [dict(row) for row in cursor.fetchall()]

                # Get students
                cursor.execute("""
                    SELECT e.*, c.name as class_name
                    FROM etudiants e
                    LEFT JOIN classes c ON e.class_id = c.id
                    ORDER BY e.name
                """)
                data['students'] = [dict(row) for row in cursor.fetchall()]

                # Get subjects
                cursor.execute("""
                    SELECT m.*, c.name as class_name
                    FROM matieres m
                    LEFT JOIN classes c ON m.class_id = c.id
                    ORDER BY m.name
                """)
                data['subjects'] = [dict(row) for row in cursor.fetchall()]

                # Get quizzes
                cursor.execute("""
                    SELECT q.*, c.name as class_name, m.name as subject_name
                    FROM quiz q
                    LEFT JOIN classes c ON q.class_id = c.id
                    LEFT JOIN matieres m ON q.subject_id = m.id
                    ORDER BY q.created_at DESC
                """)
                data['quizzes'] = [dict(row) for row in cursor.fetchall()]

                # Get attendance records
                cursor.execute("""
                    SELECT p.*, e.name as student_name, c.name as class_name, m.name as subject_name
                    FROM presences p
                    LEFT JOIN etudiants e ON p.student_id = e.id
                    LEFT JOIN classes c ON p.class_id = c.id
                    LEFT JOIN matieres m ON p.subject_id = m.id
                    ORDER BY p.date DESC, p.time DESC
                    LIMIT 100
                """)
                data['attendance'] = [dict(row) for row in cursor.fetchall()]

                return data

        except Exception as e:
            print(f"❌ Failed to get data: {e}")
            return {}

    # Get all data
    all_data = get_all_data()

    # Modern welcome section with gradient background
    welcome_section = ft.Container(
        content=ft.Column([
            ft.Row([
                ft.Icon(
                    ft.Icons.ANALYTICS,
                    size=32,
                    color=ft.Colors.WHITE
                ),
                ft.Text(
                    "Aperçu des Données Système",
                    size=28,
                    weight=ft.FontWeight.BOLD,
                    color=ft.Colors.WHITE,
                    text_align=ft.TextAlign.CENTER
                )
            ], spacing=12, alignment=ft.MainAxisAlignment.CENTER),
            ft.Text(
                "Vue complète de toutes les données du système",
                size=16,
                color=ft.Colors.WHITE70,
                text_align=ft.TextAlign.CENTER
            ),
            ft.Container(
                content=ft.Row([
                    ft.Container(
                        content=ft.Column([
                            ft.Text("Classes", size=16, weight=ft.FontWeight.BOLD, color=ft.Colors.WHITE)
                        ], spacing=4, horizontal_alignment=ft.CrossAxisAlignment.CENTER),
                        padding=ft.padding.all(12),
                        bgcolor=ft.Colors.with_opacity(0.2, ft.Colors.WHITE),
                        border_radius=12
                    ),
                    ft.Container(
                        content=ft.Column([
                            ft.Text("Matières", size=16, weight=ft.FontWeight.BOLD, color=ft.Colors.WHITE)
                        ], spacing=4, horizontal_alignment=ft.CrossAxisAlignment.CENTER),
                        padding=ft.padding.all(12),
                        bgcolor=ft.Colors.with_opacity(0.2, ft.Colors.WHITE),
                        border_radius=12
                    ),
                    ft.Container(
                        content=ft.Column([
                            ft.Text("Quiz", size=16, weight=ft.FontWeight.BOLD, color=ft.Colors.WHITE)
                        ], spacing=4, horizontal_alignment=ft.CrossAxisAlignment.CENTER),
                        padding=ft.padding.all(12),
                        bgcolor=ft.Colors.with_opacity(0.2, ft.Colors.WHITE),
                        border_radius=12
                    )
                ], spacing=16, alignment=ft.MainAxisAlignment.CENTER),
                margin=ft.margin.only(top=16)
            )
        ],
        spacing=12,
        horizontal_alignment=ft.CrossAxisAlignment.CENTER),
        padding=ft.padding.all(32),
        margin=ft.margin.only(bottom=32, top=0),
        gradient=ft.LinearGradient(
            begin=ft.alignment.top_left,
            end=ft.alignment.bottom_right,
            colors=[ft.Colors.BLUE_600, ft.Colors.PURPLE_600]
        ),
        border_radius=ft.border_radius.all(24),
        alignment=ft.alignment.center,
    )

    def create_data_table(title: str, data: list, columns: list, icon: str = ft.Icons.TABLE_CHART):
        """Create a modern data table for a specific data type."""
        if not data:
            return ft.Container(
                content=ft.Column([
                    ft.Container(
                        content=ft.Icon(
                            ft.Icons.INBOX,
                            size=48,
                            color=ft.Colors.BLUE_300
                        ),
                        width=96,
                        height=96,
                        bgcolor=ft.Colors.BLUE_50,
                        border_radius=48,
                        alignment=ft.alignment.center
                    ),
                    ft.Text(
                        f"Aucune donnée {title.lower()}",
                        size=18,
                        color=ft.Colors.BLUE_900,
                        text_align=ft.TextAlign.CENTER,
                        weight=ft.FontWeight.BOLD
                    ),
                    ft.Text(
                        f"Les données {title.lower()} apparaîtront ici",
                        size=14,
                        color=ft.Colors.GREY_600,
                        text_align=ft.TextAlign.CENTER
                    )
                ], spacing=16, horizontal_alignment=ft.CrossAxisAlignment.CENTER),
                alignment=ft.alignment.center,
                padding=ft.padding.all(48),
                bgcolor=ft.Colors.WHITE,
                border_radius=ft.border_radius.all(16),
                margin=ft.margin.only(bottom=24),
                shadow=ft.BoxShadow(
                    spread_radius=0,
                    blur_radius=12,
                    color=ft.Colors.with_opacity(0.08, ft.Colors.BLUE_600),
                    offset=ft.Offset(0, 4)
                ),
                border=ft.border.all(1, ft.Colors.BLUE_100),
                width=1200,  # Fixed narrower width
            )

        # Create table rows with simple styling
        rows = []
        for i, item in enumerate(data[:50]):  # Limit to 50 items for performance
            cells = []
            for col in columns:
                value = str(item.get(col, ''))
                if len(value) > 25:  # Shorter truncation for better layout
                    value = value[:22] + "..."
                cells.append(ft.DataCell(
                    ft.Text(
                        value,
                        size=13,
                        color=ft.Colors.GREY_800,
                        weight=ft.FontWeight.W_400
                    )
                ))

            # Cleaner alternate row colors
            row_color = ft.Colors.GREY_50 if i % 2 == 0 else ft.Colors.WHITE
            rows.append(ft.DataRow(cells=cells, color=row_color))

        # Create table columns with simple styling
        table_columns = [
            ft.DataColumn(
                ft.Text(
                    col.replace('_', ' ').title(),
                    weight=ft.FontWeight.BOLD,
                    size=14,
                    color=ft.Colors.BLUE_900
                )
            ) for col in columns
        ]

        return ft.Container(
            content=ft.Column([
                # Header with icon and title
                ft.Row([
                    ft.Container(
                        content=ft.Icon(
                            icon,
                            size=24,
                            color=ft.Colors.BLUE_600
                        ),
                        width=40,
                        height=40,
                        bgcolor=ft.Colors.BLUE_50,
                        border_radius=20,
                        alignment=ft.alignment.center
                    ),
                    ft.Column([
                        ft.Text(
                            title,
                            size=20,
                            weight=ft.FontWeight.BOLD,
                            color=ft.Colors.BLUE_900
                        ),

                    ], spacing=2, expand=True)
                ], spacing=12, alignment=ft.MainAxisAlignment.START),

                ft.Container(height=16),

                # Modern data table with improved design
                ft.Container(
                    content=ft.DataTable(
                        columns=table_columns,
                        rows=rows,
                        border=ft.border.all(1, ft.Colors.GREY_300),
                        border_radius=12,
                        vertical_lines=ft.BorderSide(1, ft.Colors.GREY_200),
                        horizontal_lines=ft.BorderSide(1, ft.Colors.GREY_200),
                        heading_row_color=ft.Colors.BLUE_50,
                        heading_row_height=48,
                        data_row_min_height=42,
                        data_row_max_height=42,
                        column_spacing=12,
                        horizontal_margin=8,
                        show_checkbox_column=False,
                    ),
                    bgcolor=ft.Colors.WHITE,
                    border_radius=12,
                    padding=ft.padding.all(12),
                    shadow=ft.BoxShadow(
                        spread_radius=0,
                        blur_radius=8,
                        color=ft.Colors.with_opacity(0.08, ft.Colors.GREY_600),
                        offset=ft.Offset(0, 2)
                    ),
                    alignment=ft.alignment.center,
                )
            ], spacing=0),
            width=800,  # Fixed narrower width for better design
            padding=ft.padding.all(20),
            margin=ft.margin.only(bottom=24),
            bgcolor=ft.Colors.WHITE,
            border_radius=ft.border_radius.all(16),
            shadow=ft.BoxShadow(
                spread_radius=0,
                blur_radius=12,
                color=ft.Colors.with_opacity(0.08, ft.Colors.GREY_600),
                offset=ft.Offset(0, 4)
            ),
            border=ft.border.all(1, ft.Colors.GREY_200),
            alignment=ft.alignment.center
        )

    # Create data sections with modern design and French labels
    data_sections = []
    data_sections.append(create_data_table("Classes", all_data.get('classes', []),
                         ['name', 'description', 'created_at'], ft.Icons.SCHOOL))
    data_sections.append(create_data_table("Étudiants", all_data.get('students', []),
                         ['name', 'class_name', 'created_at'], ft.Icons.PEOPLE))
    data_sections.append(create_data_table("Matières", all_data.get('subjects', []),
                         ['name', 'class_name', 'description', 'created_at'], ft.Icons.BOOK))
    data_sections.append(create_data_table("Quiz", all_data.get('quizzes', []),
                         ['title', 'class_name', 'subject_name', 'created_at'], ft.Icons.QUIZ))
    data_sections.append(create_data_table("Présences Récentes", all_data.get('attendance', []),
                         ['student_name', 'class_name', 'subject_name', 'status', 'date', 'time'], ft.Icons.CHECK_CIRCLE))

    # Create enhanced content
    content = [welcome_section]
    content.extend([
        ft.Container(
            content=section,
            alignment=ft.alignment.center,
        ) for section in data_sections
    ])

    return create_admin_page_layout(
        page,
        "Aperçu des Données",
        content
    )
